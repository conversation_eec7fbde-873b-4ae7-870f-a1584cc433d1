export declare class GridFSCreateProductDto {
    name: string;
    type?: string;
    price: number;
    image?: string;
    description: string;
    quantity: number;
    stock?: boolean | string;
    category: string;
    images?: string[];
    video?: string;
    discount?: string;
    id?: string;
}
export declare class GridFSUpdateProductDto {
    name?: string;
    type?: string;
    price?: number;
    image?: string;
    description?: string;
    quantity?: number;
    stock?: boolean | string;
    category?: string;
    images?: string[];
    video?: string;
    discount?: string;
    id?: string;
    keepExistingImage?: string;
    replaceAllImages?: string;
}
