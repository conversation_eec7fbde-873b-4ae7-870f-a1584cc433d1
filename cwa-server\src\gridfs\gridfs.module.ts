import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { GridFSService } from './gridfs.service';
import { GridFSProductController } from './gridfs-product.controller';
import { ImageController } from './image.controller';
import { GridFSUploadMiddleware } from './gridfs-upload.middleware';
import { ProductsModule } from '../products/products.module';

@Module({
  imports: [
    ConfigModule,
    ProductsModule, // Import ProductsModule to use ProductsService
  ],
  controllers: [GridFSProductController, ImageController],
  providers: [GridFSService, GridFSUploadMiddleware],
  exports: [GridFSService, GridFSUploadMiddleware],
})
export class GridFSModule {}
