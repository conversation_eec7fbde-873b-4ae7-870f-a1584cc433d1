import { ConfigService } from '@nestjs/config';
export declare class MulterConfigService {
    private configService;
    constructor(configService: ConfigService);
    createGridFSStorage(): import("multer-gridfs-storage/lib/gridfs").GridFsStorage;
    getMulterOptions(): {
        storage: import("multer-gridfs-storage/lib/gridfs").GridFsStorage;
        fileFilter: (req: any, file: any, cb: any) => void;
        limits: {
            fileSize: number;
        };
    };
}
