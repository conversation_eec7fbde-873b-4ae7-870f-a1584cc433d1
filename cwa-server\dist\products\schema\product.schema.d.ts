import * as mongoose from 'mongoose';
export declare class Product extends mongoose.Document {
    name: string;
    type: string;
    price: number;
    image: string;
    description: string;
    quantity: number;
    stock: boolean;
    category: string;
    images: string[];
    video: string;
    discount: string;
    id: string;
}
export declare const ProductSchema: mongoose.Schema<Product, mongoose.Model<Product, any, any, any, mongoose.Document<unknown, any, Product, any> & Product & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Product, mongoose.Document<unknown, {}, mongoose.FlatRecord<Product>, {}> & mongoose.FlatRecord<Product> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
