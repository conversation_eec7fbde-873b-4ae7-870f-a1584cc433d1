{"version": 3, "file": "gridfs-upload.interceptor.js", "sourceRoot": "", "sources": ["../../../src/gridfs/interceptors/gridfs-upload.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAMwB;AACxB,+BAAkC;AAClC,2CAA+C;AAC/C,iCAAiC;AACjC,iEAAsD;AACtD,6BAA6B;AAC7B,iCAAiC;AAG1B,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAGlC,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAC9C,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,gBAAgB;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,aAAa,CAAC,CAAC;QAE/D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,qCAAa,CAAC;YAChC,GAAG,EAAE,QAAQ;YACb,OAAO,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE;YAC5D,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBAClB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrC,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;wBAClC,IAAI,GAAG,EAAE,CAAC;4BACR,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;wBACrB,CAAC;wBAED,MAAM,QAAQ,GAAG;4BACf,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;4BACzG,UAAU,EAAE,SAAS;4BACrB,QAAQ,EAAE;gCACR,YAAY,EAAE,IAAI,CAAC,YAAY;gCAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gCACvB,UAAU,EAAE,IAAI,IAAI,EAAE;6BACvB;yBACF,CAAC;wBAEF,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACpB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,CAAC,GAAQ,EAAE,IAAS,EAAE,EAAO,EAAE,EAAE;YAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,EAAE,CAAC,IAAI,KAAK,CAAC,0CAA0C,CAAC,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACnB,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,UAAU;YACtB,MAAM,EAAE;gBACN,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;aAC1B;SACF,CAAC,CAAC;IACL,CAAC;IAED,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;QAEtD,OAAO,IAAI,iBAAU,CAAC,CAAC,QAAQ,EAAE,EAAE;YACjC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;gBACtC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE;gBAC9B,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,CAAC,EAAE;aAC1C,CAAC,CAAC;YAEH,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;gBACxC,IAAI,KAAK,EAAE,CAAC;oBACV,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;oBACtC,QAAQ,CAAC,KAAK,CAAC,IAAI,4BAAmB,CAAC;wBACrC,MAAM,EAAE,OAAO;wBACf,OAAO,EAAE,oBAAoB;wBAC7B,KAAK,EAAE,KAAK,CAAC,OAAO;qBACrB,CAAC,CAAC,CAAC;gBACN,CAAC;qBAAM,CAAC;oBAEN,IAAI,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC;wBACtB,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;wBACnC,KAAK,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC;wBACnC,QAAQ,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE;qBACpC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAtFY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAIwB,sBAAa;GAHrC,uBAAuB,CAsFnC"}