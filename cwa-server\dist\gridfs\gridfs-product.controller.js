"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GridFSProductController = void 0;
const common_1 = require("@nestjs/common");
const products_service_1 = require("../products/products.service");
const gridfs_product_dto_1 = require("./dto/gridfs-product.dto");
const admin_guard_1 = require("../auth/admin.guard");
const gridfs_service_1 = require("./gridfs.service");
const gridfs_upload_interceptor_1 = require("./interceptors/gridfs-upload.interceptor");
let GridFSProductController = class GridFSProductController {
    constructor(productsService, gridfsService) {
        this.productsService = productsService;
        this.gridfsService = gridfsService;
    }
    async getAllProducts(query) {
        try {
            const result = await this.productsService.findAll(query);
            return {
                status: 'success',
                results: result.results,
                data: {
                    products: result.products,
                },
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Error fetching products',
            }, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getProduct(id) {
        try {
            const product = await this.productsService.findById(id);
            return {
                status: 'success',
                data: {
                    product,
                },
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Error fetching product',
            }, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createProduct(createProductDto, files) {
        try {
            const productData = { ...createProductDto };
            if (productData.stock === 'true')
                productData.stock = true;
            if (productData.stock === 'false')
                productData.stock = false;
            if (files && files.image && files.image.length > 0) {
                productData.image = files.image[0].filename;
            }
            if (files && files.additionalImages && files.additionalImages.length > 0) {
                productData.images = files.additionalImages.map((file) => file.filename);
            }
            console.log('Product data to be saved:', productData);
            const createProductData = {
                ...productData,
                stock: typeof productData.stock === 'string' ? productData.stock === 'true' : productData.stock,
            };
            const product = await this.productsService.createProduct(createProductData);
            return {
                status: 'success',
                data: {
                    product,
                },
            };
        }
        catch (error) {
            console.error('Create product error:', error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to create product',
            }, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateProduct(id, updateProductDto, files) {
        try {
            const existingProduct = await this.productsService.findById(id);
            const productData = { ...updateProductDto };
            if (productData.stock === 'true')
                productData.stock = true;
            if (productData.stock === 'false')
                productData.stock = false;
            if (files && files.image && files.image.length > 0) {
                if (existingProduct.image) {
                    await this.gridfsService.deleteFileByFilename(existingProduct.image);
                }
                productData.image = files.image[0].filename;
            }
            else if (updateProductDto.keepExistingImage === 'true' &&
                existingProduct.image) {
                productData.image = existingProduct.image;
            }
            if (files && files.additionalImages && files.additionalImages.length > 0) {
                if (updateProductDto.replaceAllImages === 'true') {
                    if (existingProduct.images && existingProduct.images.length > 0) {
                        for (const imageFilename of existingProduct.images) {
                            await this.gridfsService.deleteFileByFilename(imageFilename);
                        }
                    }
                    productData.images = files.additionalImages.map((file) => file.filename);
                }
                else {
                    const existingImages = existingProduct.images || [];
                    const newImages = files.additionalImages.map((file) => file.filename);
                    productData.images = [...existingImages, ...newImages];
                }
            }
            else if (existingProduct.images && existingProduct.images.length > 0) {
                productData.images = existingProduct.images;
            }
            delete productData.keepExistingImage;
            delete productData.replaceAllImages;
            console.log('Product data to be saved:', productData);
            const updateProductData = {
                ...productData,
                stock: typeof productData.stock === 'string' ? productData.stock === 'true' : productData.stock,
            };
            delete updateProductData.keepExistingImage;
            delete updateProductData.replaceAllImages;
            const product = await this.productsService.updateProduct(id, updateProductData);
            return {
                status: 'success',
                data: {
                    product,
                },
            };
        }
        catch (error) {
            console.error('Update product error:', error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to update product',
            }, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteProduct(id) {
        try {
            const product = await this.productsService.findById(id);
            if (product.image) {
                await this.gridfsService.deleteFileByFilename(product.image);
            }
            if (product.images && product.images.length > 0) {
                for (const imageFilename of product.images) {
                    await this.gridfsService.deleteFileByFilename(imageFilename);
                }
            }
            await this.productsService.deleteProduct(id);
            return {
                status: 'success',
                message: 'Product deleted successfully',
            };
        }
        catch (error) {
            console.error('Delete product error:', error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to delete product',
            }, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.GridFSProductController = GridFSProductController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GridFSProductController.prototype, "getAllProducts", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GridFSProductController.prototype, "getProduct", null);
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    (0, common_1.UseInterceptors)(gridfs_upload_interceptor_1.GridFSUploadInterceptor),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFiles)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [gridfs_product_dto_1.GridFSCreateProductDto, Object]),
    __metadata("design:returntype", Promise)
], GridFSProductController.prototype, "createProduct", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    (0, common_1.UseInterceptors)(gridfs_upload_interceptor_1.GridFSUploadInterceptor),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFiles)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, gridfs_product_dto_1.GridFSUpdateProductDto, Object]),
    __metadata("design:returntype", Promise)
], GridFSProductController.prototype, "updateProduct", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GridFSProductController.prototype, "deleteProduct", null);
exports.GridFSProductController = GridFSProductController = __decorate([
    (0, common_1.Controller)('gridfs-products'),
    __metadata("design:paramtypes", [products_service_1.ProductsService,
        gridfs_service_1.GridFSService])
], GridFSProductController);
//# sourceMappingURL=gridfs-product.controller.js.map