"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const product_schema_1 = require("./schema/product.schema");
let ProductsService = class ProductsService {
    constructor(productModel) {
        this.productModel = productModel;
    }
    async createProduct(createProductDto) {
        try {
            const productData = {
                ...createProductDto,
                stock: createProductDto.stock !== undefined ? createProductDto.stock : true,
                type: createProductDto.type || 'regular',
            };
            const product = new this.productModel(productData);
            return await product.save();
        }
        catch (error) {
            if (error.code === 11000) {
                throw new common_1.BadRequestException('Product with this name already exists');
            }
            throw new common_1.BadRequestException(error.message);
        }
    }
    async findAll(query = {}) {
        try {
            const queryObj = {};
            if (query.category) {
                queryObj.category = query.category;
            }
            if (query.type) {
                queryObj.type = query.type;
            }
            if (query.stock !== undefined) {
                queryObj.stock = query.stock;
            }
            if (query.price) {
                queryObj.price = {};
                if (query.price.gte !== undefined)
                    queryObj.price.$gte = query.price.gte;
                if (query.price.lte !== undefined)
                    queryObj.price.$lte = query.price.lte;
                if (query.price.gt !== undefined)
                    queryObj.price.$gt = query.price.gt;
                if (query.price.lt !== undefined)
                    queryObj.price.$lt = query.price.lt;
            }
            let mongoQuery = this.productModel.find(queryObj);
            if (query.sort) {
                const sortBy = query.sort.split(',').join(' ');
                mongoQuery = mongoQuery.sort(sortBy);
            }
            else {
                mongoQuery = mongoQuery.sort('-createdAt');
            }
            if (query.fields) {
                const fields = query.fields.split(',').join(' ');
                mongoQuery = mongoQuery.select(fields);
            }
            else {
                mongoQuery = mongoQuery.select('-__v');
            }
            const page = query.page || 1;
            const limit = query.limit || 10;
            const skip = (page - 1) * limit;
            mongoQuery = mongoQuery.skip(skip).limit(limit);
            const products = await mongoQuery.exec();
            const results = products.length;
            return { products, results };
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async findById(id) {
        try {
            const product = await this.productModel.findById(id).select('-__v').exec();
            if (!product) {
                throw new common_1.NotFoundException('Product not found');
            }
            return product;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Invalid product ID');
        }
    }
    async updateProduct(id, updateProductDto) {
        try {
            const product = await this.productModel.findByIdAndUpdate(id, updateProductDto, {
                new: true,
                runValidators: true,
            }).select('-__v').exec();
            if (!product) {
                throw new common_1.NotFoundException('Product not found');
            }
            return product;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException(error.message);
        }
    }
    async deleteProduct(id) {
        try {
            const result = await this.productModel.findByIdAndDelete(id).exec();
            if (!result) {
                throw new common_1.NotFoundException('Product not found');
            }
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Invalid product ID');
        }
    }
};
exports.ProductsService = ProductsService;
exports.ProductsService = ProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(product_schema_1.Product.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], ProductsService);
//# sourceMappingURL=products.service.js.map