"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GridFSService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const mongodb_1 = require("mongodb");
let GridFSService = class GridFSService {
    constructor(connection) {
        this.connection = connection;
    }
    async onModuleInit() {
        await this.connectGridFS();
    }
    async connectGridFS() {
        try {
            if (this.connection.readyState !== 1) {
                throw new Error('MongoDB not connected');
            }
            this.gridFSBucket = new mongodb_1.GridFSBucket(this.connection.db, {
                bucketName: 'uploads'
            });
            console.log('GridFS connected successfully');
            return true;
        }
        catch (error) {
            console.error('GridFS connection error:', error);
            return false;
        }
    }
    getGridFSBucket() {
        if (!this.gridFSBucket) {
            throw new Error('GridFS bucket not initialized');
        }
        return this.gridFSBucket;
    }
    async findFileByFilename(filename) {
        try {
            const bucket = this.getGridFSBucket();
            const files = await bucket.find({ filename }).toArray();
            return files.length > 0 ? files[0] : null;
        }
        catch (error) {
            console.error('Error finding file by filename:', error);
            return null;
        }
    }
    async findFileById(id) {
        try {
            const bucket = this.getGridFSBucket();
            const files = await bucket.find({ _id: new mongodb_1.ObjectId(id) }).toArray();
            return files.length > 0 ? files[0] : null;
        }
        catch (error) {
            console.error('Error finding file by ID:', error);
            return null;
        }
    }
    async deleteFileByFilename(filename) {
        try {
            const file = await this.findFileByFilename(filename);
            if (!file) {
                return false;
            }
            const bucket = this.getGridFSBucket();
            await bucket.delete(file._id);
            return true;
        }
        catch (error) {
            console.error('Error deleting file by filename:', error);
            return false;
        }
    }
    async deleteFileById(id) {
        try {
            const bucket = this.getGridFSBucket();
            await bucket.delete(new mongodb_1.ObjectId(id));
            return true;
        }
        catch (error) {
            console.error('Error deleting file by ID:', error);
            return false;
        }
    }
    createReadStream(fileId) {
        try {
            const bucket = this.getGridFSBucket();
            return bucket.openDownloadStream(new mongodb_1.ObjectId(fileId));
        }
        catch (error) {
            console.error('Error creating read stream:', error);
            throw error;
        }
    }
    createReadStreamByFilename(filename) {
        try {
            const bucket = this.getGridFSBucket();
            return bucket.openDownloadStreamByName(filename);
        }
        catch (error) {
            console.error('Error creating read stream by filename:', error);
            throw error;
        }
    }
};
exports.GridFSService = GridFSService;
exports.GridFSService = GridFSService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectConnection)()),
    __metadata("design:paramtypes", [mongoose_2.Connection])
], GridFSService);
//# sourceMappingURL=gridfs.service.js.map