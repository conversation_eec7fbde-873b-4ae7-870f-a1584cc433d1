import { Model } from 'mongoose';
import { IProduct, IProductQuery } from './interfaces/product.interface';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
export declare class ProductsService {
    private productModel;
    constructor(productModel: Model<IProduct>);
    createProduct(createProductDto: CreateProductDto): Promise<IProduct>;
    findAll(query?: IProductQuery): Promise<{
        products: IProduct[];
        results: number;
    }>;
    findById(id: string): Promise<IProduct>;
    updateProduct(id: string, updateProductDto: UpdateProductDto): Promise<IProduct>;
    deleteProduct(id: string): Promise<void>;
}
