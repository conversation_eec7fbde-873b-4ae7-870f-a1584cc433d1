import { OnModuleInit } from '@nestjs/common';
import { Connection } from 'mongoose';
import { GridFSBucket } from 'mongodb';
import { Readable } from 'stream';
export declare class GridFSService implements OnModuleInit {
    private connection;
    private gridFSBucket;
    constructor(connection: Connection);
    onModuleInit(): Promise<void>;
    private connectGridFS;
    getGridFSBucket(): GridFSBucket;
    findFileByFilename(filename: string): Promise<any>;
    findFileById(id: string): Promise<any>;
    deleteFileByFilename(filename: string): Promise<boolean>;
    deleteFileById(id: string): Promise<boolean>;
    createReadStream(fileId: string): Readable;
    createReadStreamByFilename(filename: string): Readable;
}
