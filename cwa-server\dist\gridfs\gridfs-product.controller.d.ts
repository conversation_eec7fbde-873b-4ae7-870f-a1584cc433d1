import { ProductsService } from '../products/products.service';
import { GridFSCreateProductDto, GridFSUpdateProductDto } from './dto/gridfs-product.dto';
import { IProductResponse, IProductQuery } from '../products/interfaces/product.interface';
import { GridFSService } from './gridfs.service';
interface MulterFile {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    size: number;
    filename: string;
    metadata: any;
    bucketName: string;
    chunkSize: number;
    id: any;
}
export declare class GridFSProductController {
    private readonly productsService;
    private readonly gridfsService;
    constructor(productsService: ProductsService, gridfsService: GridFSService);
    getAllProducts(query: IProductQuery): Promise<IProductResponse>;
    getProduct(id: string): Promise<IProductResponse>;
    createProduct(createProductDto: GridFSCreateProductDto, files: {
        image?: MulterFile[];
        additionalImages?: MulterFile[];
    }): Promise<IProductResponse>;
    updateProduct(id: string, updateProductDto: GridFSUpdateProductDto, files: {
        image?: MulterFile[];
        additionalImages?: MulterFile[];
    }): Promise<IProductResponse>;
    deleteProduct(id: string): Promise<{
        status: string;
        message: string;
    }>;
}
export {};
