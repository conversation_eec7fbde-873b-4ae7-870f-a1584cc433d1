"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GridFSModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const gridfs_service_1 = require("./gridfs.service");
const gridfs_product_controller_1 = require("./gridfs-product.controller");
const image_controller_1 = require("./image.controller");
const gridfs_upload_middleware_1 = require("./gridfs-upload.middleware");
const multer_config_service_1 = require("./multer-config.service");
const gridfs_upload_interceptor_1 = require("./interceptors/gridfs-upload.interceptor");
const products_module_1 = require("../products/products.module");
const auth_module_1 = require("../auth/auth.module");
const users_module_1 = require("../users/users.module");
let GridFSModule = class GridFSModule {
};
exports.GridFSModule = GridFSModule;
exports.GridFSModule = GridFSModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            products_module_1.ProductsModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
        ],
        controllers: [gridfs_product_controller_1.GridFSProductController, image_controller_1.ImageController],
        providers: [gridfs_service_1.GridFSService, gridfs_upload_middleware_1.GridFSUploadMiddleware, multer_config_service_1.MulterConfigService, gridfs_upload_interceptor_1.GridFSUploadInterceptor],
        exports: [gridfs_service_1.GridFSService, gridfs_upload_middleware_1.GridFSUploadMiddleware, multer_config_service_1.MulterConfigService, gridfs_upload_interceptor_1.GridFSUploadInterceptor],
    })
], GridFSModule);
//# sourceMappingURL=gridfs.module.js.map