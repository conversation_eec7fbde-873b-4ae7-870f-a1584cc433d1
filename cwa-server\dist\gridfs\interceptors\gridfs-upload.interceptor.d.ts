import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ConfigService } from '@nestjs/config';
export declare class GridFSUploadInterceptor implements NestInterceptor {
    private configService;
    private upload;
    constructor(configService: ConfigService);
    private initializeUpload;
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
}
